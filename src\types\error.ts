// Error handling types

export interface ErrorInfo {
  componentStack: string
  errorBoundary?: string
  errorBoundaryStack?: string
}

export interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

export interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  isolate?: boolean // Whether to isolate this boundary from parent boundaries
  level?: 'page' | 'section' | 'component' // Error boundary level for different handling
}

export interface ErrorFallbackProps {
  error: Error | null
  errorInfo: ErrorInfo | null
  resetError: () => void
  level?: 'page' | 'section' | 'component'
}

// Error categories for better handling
export type ErrorCategory = 
  | 'network'
  | 'validation'
  | 'authentication'
  | 'authorization'
  | 'system'
  | 'unknown'

export interface CategorizedError extends Error {
  category: ErrorCategory
  code?: string
  statusCode?: number
  retryable?: boolean
  userMessage?: string
}

// Error reporting interface
export interface ErrorReport {
  id: string
  timestamp: Date
  error: Error
  errorInfo: ErrorInfo
  userAgent: string
  url: string
  userId?: string
  level: 'page' | 'section' | 'component'
  category: ErrorCategory
  context?: Record<string, any>
}
