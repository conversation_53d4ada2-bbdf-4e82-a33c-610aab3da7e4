import { ThemeProvider } from "@/components/theme-provider"
import { NotificationProvider } from "@/contexts/NotificationContext"
import { ToastContainer } from "@/components/ui/feedback"
import { PageErrorBoundary } from "@/components/error"
import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import MainPage from "@/pages/MainPage"
import Dashboard from "@/pages/app/Dashboard"
import AddClient from "@/pages/app/client/AddClient"
import ClientList from "@/pages/app/client/ClientList"
import ProtectedRoute from "@/components/ProtectedRoute"
import NotificationExample from "@/pages/hidden/notificationExample"
import NotFound from "@/pages/NotFound"

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="planfuly-ui-theme">
      <NotificationProvider>
        <PageErrorBoundary>
          <Router>
            <Routes>
              <Route path="/" element={<MainPage />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/clients"
                element={
                  <ProtectedRoute>
                    <ClientList />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/clients/new"
                element={
                  <ProtectedRoute>
                    <AddClient />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/hidden/notificationExample"
                element={
                  <ProtectedRoute>
                    <NotificationExample />
                  </ProtectedRoute>
                }
              />
              {/* Catch-all route for 404 */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Router>
        </PageErrorBoundary>
        <ToastContainer />
      </NotificationProvider>
    </ThemeProvider>
  )
}

export default App
