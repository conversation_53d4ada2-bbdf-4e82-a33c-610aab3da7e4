import { Moon, Sun } from "lucide-react"

import { But<PERSON> } from "@/components/ui/basic/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/complex/dropdown-menu"
import { useTheme } from "@/components/theme-provider"

export function ModeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuRadioGroup value={theme} onValueChange={(v) => setTheme(v as "light" | "dark" | "system")}>
          <DropdownMenuRadioItem value="light" className="data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground">
            Light
          </DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="dark" className="data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground">
            Dark <sup>(beta)</sup>
          </DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="system" className="data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground">
            System
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
