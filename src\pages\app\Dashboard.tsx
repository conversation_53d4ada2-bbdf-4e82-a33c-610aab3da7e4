import { Layout } from "@/components/ui/layout"
import { But<PERSON> } from "@/components/ui/basic/button"
import { useNavigate } from "react-router-dom"

function Dashboard() {
  const navigate = useNavigate()

  const goToAdminMode = () => navigate("/clients")
  const goToPresentationMode = () => { }

  return (
    <Layout title="">
        <div className="flex-1 flex items-center justify-center gap-4">
          <Button size="lg" onClick={goToAdminMode}>Admin Mode</Button>
          <Button size="lg" variant="callToAction" onClick={goToPresentationMode}>Presentation Mode</Button>
        </div>
    </Layout>
  )
}

export default Dashboard
