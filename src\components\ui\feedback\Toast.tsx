import { useEffect, useState } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/basic/button'
import type { Notification } from '@/types/notification'

// Toast variants
const toastVariants = cva(
  "relative flex w-full max-w-sm items-start gap-3 rounded-lg border p-4 shadow-lg transition-all duration-300 ease-in-out",
  {
    variants: {
      variant: {
        success: "border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100",
        error: "border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100",
        warning: "border-orange-200 bg-orange-50 text-orange-900 dark:border-orange-800 dark:bg-orange-950 dark:text-orange-100",
        info: "border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100"
      },
      state: {
        entering: "animate-in slide-in-from-right-full",
        entered: "animate-in slide-in-from-right-0",
        exiting: "animate-out slide-out-to-right-full"
      }
    },
    defaultVariants: {
      variant: "info",
      state: "entered"
    }
  }
)

// Icon mapping
const iconMap = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info
}

// Icon variants
const iconVariants = cva("h-5 w-5 shrink-0", {
  variants: {
    variant: {
      success: "text-green-600 dark:text-green-400",
      error: "text-red-600 dark:text-red-400",
      warning: "text-orange-600 dark:text-orange-400",
      info: "text-blue-600 dark:text-blue-400"
    }
  }
})

interface ToastProps extends VariantProps<typeof toastVariants> {
  notification: Notification
  onDismiss: (id: string) => void
  className?: string
}

export function Toast({ notification, onDismiss, className }: ToastProps) {
  const [state, setState] = useState<'entering' | 'entered' | 'exiting'>('entering')
  const Icon = iconMap[notification.type]

  useEffect(() => {
    // Animate in
    const enterTimer = setTimeout(() => setState('entered'), 50)
    return () => clearTimeout(enterTimer)
  }, [])

  const handleDismiss = () => {
    setState('exiting')
    // Wait for animation to complete before removing
    setTimeout(() => onDismiss(notification.id), 300)
  }

  const handleAction = () => {
    if (notification.action) {
      notification.action.onClick()
      handleDismiss()
    }
  }

  return (
    <div
      className={cn(
        toastVariants({ variant: notification.type, state }),
        className
      )}
      role="alert"
      aria-live="polite"
    >
      {/* Icon */}
      <Icon className={iconVariants({ variant: notification.type })} />

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm leading-5">
          {notification.title}
        </div>
        {notification.message && (
          <div className="mt-1 text-sm opacity-90 leading-5">
            {notification.message}
          </div>
        )}
        
        {/* Action button */}
        {notification.action && (
          <div className="mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction}
              className="h-8 px-3 text-xs"
            >
              {notification.action.label}
            </Button>
          </div>
        )}
      </div>

      {/* Dismiss button */}
      {notification.dismissible && (
        <button
          onClick={handleDismiss}
          className="shrink-0 rounded-md p-1 hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
          aria-label="Dismiss notification"
        >
          <X className="h-4 w-4 opacity-60 hover:opacity-100" />
        </button>
      )}

      {/* Progress bar for timed notifications */}
      {notification.duration && notification.duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/10 dark:bg-white/10 rounded-b-lg overflow-hidden">
          <div
            className="h-full bg-current opacity-30 transition-all ease-linear"
            style={{
              animation: `toast-progress ${notification.duration}ms linear forwards`
            }}
          />
        </div>
      )}
    </div>
  )
}

// CSS animation for progress bar (to be added to global styles)
export const toastProgressAnimation = `
@keyframes toast-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
`
