import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/basic/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/container/card'
import { Alert } from '@/components/ui/feedback'
import { ComponentErrorBoundary } from '@/components/error'
import { useError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler'
import { createCategorizedError } from '@/lib/error'

/**
 * Demo component to showcase the error handling system
 * This component demonstrates different types of errors and how they're handled
 */
export function ErrorHandlingDemo() {
  const { handleError, handleAsync, notifications } = useErrorHandler({
    context: { component: 'ErrorHandlingDemo' }
  })
  const [loading, setLoading] = useState(false)

  // Component that throws an error (for error boundary demo)
  const ErrorThrowingComponent = () => {
    throw new Error('This is a test component error for the error boundary')
  }

  // Simulate different types of errors
  const simulateNetworkError = async () => {
    setLoading(true)
    await handleError(
      createCategorizedError('Failed to connect to server', 'network', {
        statusCode: 503,
        retryable: true,
        userMessage: 'Unable to connect to the server. Please check your internet connection.'
      })
    )
    setLoading(false)
  }

  const simulateValidationError = () => {
    notifications.showValidationError({
      title: 'Form Validation Failed',
      message: 'Please correct the following errors:',
      validationErrors: {
        email: ['Email is required', 'Email format is invalid'],
        password: ['Password must be at least 8 characters']
      }
    })
  }

  const simulateSystemError = () => {
    notifications.showSystemError({
      title: 'Database Connection Failed',
      message: 'Unable to connect to the database. Our team has been notified.',
      component: 'ErrorHandlingDemo'
    })
  }

  const simulateAsyncOperation = async () => {
    setLoading(true)
    
    const { data, error } = await handleAsync(
      async () => {
        // Simulate an async operation that might fail
        await new Promise(resolve => setTimeout(resolve, 1000))
        if (Math.random() > 0.5) {
          throw createCategorizedError('Random async error', 'system', {
            userMessage: 'The operation failed randomly for demo purposes'
          })
        }
        return { success: true, message: 'Operation completed successfully!' }
      },
      { context: { operation: 'simulateAsyncOperation' } }
    )

    if (data) {
      notifications.showSuccess('Success!', data.message)
    } else if (error) {
      // Error is already handled by the error handler
      console.log('Operation failed:', error.message)
    }
    
    setLoading(false)
  }

  const [showErrorComponent, setShowErrorComponent] = useState(false)

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Error Handling System Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="info">
            This demo showcases the comprehensive error handling system. Try the different buttons to see various error types and handling mechanisms.
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h3 className="font-semibold">Notification Errors</h3>
              <Button 
                onClick={simulateNetworkError} 
                variant="outline" 
                className="w-full"
                disabled={loading}
              >
                Network Error
              </Button>
              <Button 
                onClick={simulateValidationError} 
                variant="outline" 
                className="w-full"
              >
                Validation Error
              </Button>
              <Button 
                onClick={simulateSystemError} 
                variant="outline" 
                className="w-full"
              >
                System Error
              </Button>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold">Async Operations</h3>
              <Button 
                onClick={simulateAsyncOperation} 
                variant="outline" 
                className="w-full"
                disabled={loading}
              >
                {loading ? 'Processing...' : 'Random Async Operation'}
              </Button>
              <Button 
                onClick={() => notifications.showSuccess('Success!', 'This is a success message')} 
                variant="outline" 
                className="w-full"
              >
                Show Success
              </Button>
              <Button 
                onClick={() => notifications.showWarning('Warning!', 'This is a warning message')} 
                variant="outline" 
                className="w-full"
              >
                Show Warning
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="font-semibold">Error Boundary Demo</h3>
            <div className="flex gap-2">
              <Button 
                onClick={() => setShowErrorComponent(!showErrorComponent)} 
                variant={showErrorComponent ? "destructive" : "outline"}
                className="flex-1"
              >
                {showErrorComponent ? 'Hide Error Component' : 'Show Error Component'}
              </Button>
            </div>

            {showErrorComponent && (
              <ComponentErrorBoundary>
                <ErrorThrowingComponent />
              </ComponentErrorBoundary>
            )}
          </div>

          <Alert variant="warning">
            <strong>Note:</strong> In development mode, you'll see detailed error information. In production, users will see user-friendly messages while detailed errors are logged for developers.
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}
