import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface Heading1Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading1 = forwardRef<HTMLHeadingElement, Heading1Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h1
        ref={ref}
        className={cn(
          "text-5xl font-extrabold leading-tight text-foreground",
          className
        )}
        {...props}
      >
        {children}
      </h1>
    );
  }
);

Heading1.displayName = 'Heading1';

export { Heading1 };
