import { useState, useEffect, useCallback, useMemo } from 'react'
import { clientService } from '@/services'
import { useErrorHand<PERSON> } from './useErrorHandler'
import type { Client, CreateClientData, UpdateClientData, RepositoryResult } from '@/types'

interface UseClientsState {
  clients: Client[]
  loading: boolean
  error: string | null
}

interface UseClientsActions {
  createClient: (data: CreateClientData) => Promise<RepositoryResult<Client>>
  updateClient: (id: string, data: UpdateClientData) => Promise<RepositoryResult<Client>>
  deleteClient: (id: string) => Promise<RepositoryResult<void>>
  refetch: () => void
  clearError: () => void
}

interface UseClientsReturn extends UseClientsState, UseClientsActions {
  stats: {
    total: number
    active: number
    prospects: number
    inactive: number
  }
}

/**
 * Custom hook for managing clients with real-time subscriptions
 * Provides CRUD operations and loading/error states
 */
export function useClients(): UseClientsReturn {
  // Memoize the options object to prevent handleError from being recreated on every render
  const errorHandlerOptions = useMemo(() => ({
    context: { hook: 'useClients' },
    showNotifications: false // Let components handle notifications
  }), [])

  const { handleError } = useErrorHandler(errorHandlerOptions)

  const [state, setState] = useState<UseClientsState>({
    clients: [],
    loading: true,
    error: null,
  })

  // Set up real-time subscription
  useEffect(() => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    const unsubscribe = clientService.subscribeToClients(
      (clients) => {
        setState({
          clients,
          loading: false,
          error: null,
        })
      },
      async (error) => {
        const categorizedError = await handleError(error, {
          context: { action: 'subscribeToClients' },
          silent: true // Don't show notifications, just categorize
        })

        setState(prev => ({
          ...prev,
          loading: false,
          error: categorizedError.userMessage || 'Failed to load clients',
        }))
      }
    )

    // Cleanup subscription on unmount
    return () => unsubscribe()
  }, [handleError])

  // CRUD operations
  const createClient = useCallback(async (data: CreateClientData): Promise<RepositoryResult<Client>> => {
    const result = await clientService.createClient(data)

    if (!result.success) {
      const categorizedError = await handleError(new Error(result.error || 'Failed to create client'), {
        context: { action: 'createClient', data },
        silent: true
      })
      setState(prev => ({ ...prev, error: categorizedError.userMessage || 'Failed to create client' }))
    }

    return result
  }, [handleError])

  const updateClient = useCallback(async (id: string, data: UpdateClientData): Promise<RepositoryResult<Client>> => {
    const result = await clientService.updateClient(id, data)
    
    if (!result.success) {
      setState(prev => ({ ...prev, error: result.error || 'Failed to update client' }))
    }
    
    return result
  }, [])

  const deleteClient = useCallback(async (id: string): Promise<RepositoryResult<void>> => {
    const result = await clientService.deleteClient(id)
    
    if (!result.success) {
      setState(prev => ({ ...prev, error: result.error || 'Failed to delete client' }))
    }
    
    return result
  }, [])

  const refetch = useCallback(() => {
    // The real-time subscription will automatically refetch
    // This is here for consistency with typical hook patterns
    setState(prev => ({ ...prev, loading: true, error: null }))
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Calculate stats
  const stats = clientService.getClientStats(state.clients)

  return {
    ...state,
    stats,
    createClient,
    updateClient,
    deleteClient,
    refetch,
    clearError,
  }
}
