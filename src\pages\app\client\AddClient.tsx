import { Layout } from "@/components/ui/layout"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/container/card"
import { Input } from "@/components/ui/form/input"
import { <PERSON><PERSON> } from "@/components/ui/basic/button"
import { Alert } from "@/components/ui/feedback"
import { SectionErrorBoundary } from "@/components/error"
import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useClients } from "@/hooks"
import { useErrorHandler } from "@/hooks/useErrorHandler"
import type { CreateClientData } from "@/types"

function AddClient() {
  const navigate = useNavigate()
  const { createClient } = useClients()
  const { handleAsync, handleValidationError } = useErrorHandler({
    context: { component: 'AddClient' }
  })

  const [formData, setFormData] = useState<CreateClientData>({
    name: "",
    status: "Prospect",
    contactEmail: "",
    contactPhone: "",
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (field: keyof CreateClientData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value || undefined
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) {
      handleValidationError("Client name is required", { name: ["Name is required"] })
      return
    }

    setLoading(true)
    setError(null)

    const { data: result, error: handlerError } = await handleAsync(
      () => createClient({
        ...formData,
        name: formData.name.trim(),
        contactEmail: formData.contactEmail?.trim() || undefined,
        contactPhone: formData.contactPhone?.trim() || undefined,
      }),
      {
        showNotification: false, // We'll handle success/error manually
        context: { action: 'createClient', formData }
      }
    )

    if (result?.success) {
      navigate("/clients")
    } else if (handlerError) {
      setError(handlerError.userMessage || "Failed to create client")
    } else if (result && !result.success) {
      setError(result.error || "Failed to create client")
    }

    setLoading(false)
  }

  return (
    <Layout title="Add Client">
      <div className="max-w-xl mx-auto px-4 py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">New Client</CardTitle>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert
                variant="error"
                title="Failed to create client"
                dismissible
                onDismiss={() => setError(null)}
                className="mb-4"
              >
                {error}
              </Alert>
            )}

            <SectionErrorBoundary>
              <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-1.5">
                <label className="text-sm font-medium">Name *</label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Client name"
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-1.5">
                <label className="text-sm font-medium">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                  disabled={loading}
                >
                  <option value="Prospect">Prospect</option>
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                </select>
              </div>

              <div className="space-y-1.5">
                <label className="text-sm font-medium">Email</label>
                <Input
                  type="email"
                  value={formData.contactEmail || ""}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  disabled={loading}
                />
              </div>

              <div className="space-y-1.5">
                <label className="text-sm font-medium">Phone</label>
                <Input
                  value={formData.contactPhone || ""}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="(*************"
                  disabled={loading}
                />
              </div>

              <div className="flex items-center gap-3 pt-2">
                <Button type="submit" disabled={loading}>
                  {loading ? "Saving..." : "Save"}
                </Button>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => navigate(-1)}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </form>
            </SectionErrorBoundary>
          </CardContent>
          <CardFooter />
        </Card>
      </div>
    </Layout>
  )
}

export default AddClient

