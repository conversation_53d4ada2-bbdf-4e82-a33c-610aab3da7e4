import { } from 'react'
import { createPortal } from 'react-dom'
import { cva } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { Toast } from './Toast'
import { useNotifications } from '@/contexts/NotificationContext'
import type { NotificationPosition } from '@/types/notification'

// Container position variants
const containerVariants = cva(
  "fixed z-50 flex flex-col gap-2 p-4 pointer-events-none",
  {
    variants: {
      position: {
        'top-left': "top-0 left-0",
        'top-center': "top-0 left-1/2 -translate-x-1/2",
        'top-right': "top-0 right-0",
        'bottom-left': "bottom-0 left-0",
        'bottom-center': "bottom-0 left-1/2 -translate-x-1/2",
        'bottom-right': "bottom-0 right-0"
      }
    },
    defaultVariants: {
      position: 'top-right'
    }
  }
)

interface ToastContainerProps {
  position?: NotificationPosition
  maxToasts?: number
  className?: string
}

export function ToastContainer({ 
  position = 'top-right', 
  maxToasts = 5,
  className 
}: ToastContainerProps) {
  const { notifications, removeNotification } = useNotifications()

  // Limit the number of visible toasts
  const visibleNotifications = notifications.slice(0, maxToasts)

  // Don't render if no notifications
  if (visibleNotifications.length === 0) {
    return null
  }

  // Determine if toasts should be reversed based on position
  const shouldReverse = position.startsWith('bottom')
  const displayNotifications = shouldReverse 
    ? [...visibleNotifications].reverse() 
    : visibleNotifications

  return createPortal(
    <div
      className={cn(containerVariants({ position }), className)}
      aria-live="polite"
      aria-label="Notifications"
    >
      {displayNotifications.map((notification) => (
        <div key={notification.id} className="pointer-events-auto">
          <Toast
            notification={notification}
            onDismiss={removeNotification}
          />
        </div>
      ))}
    </div>,
    document.body
  )
}
