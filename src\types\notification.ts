// Notification system types

export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export type NotificationPosition = 
  | 'top-left' 
  | 'top-center' 
  | 'top-right' 
  | 'bottom-left' 
  | 'bottom-center' 
  | 'bottom-right'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message?: string
  duration?: number // in milliseconds, 0 means persistent
  dismissible?: boolean
  action?: {
    label: string
    onClick: () => void
  }
  createdAt: Date
}

export interface NotificationOptions {
  type?: NotificationType
  title: string
  message?: string
  duration?: number
  dismissible?: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

// Error-specific notification types
export interface ErrorNotificationOptions extends Omit<NotificationOptions, 'type'> {
  error?: Error | unknown
  errorCode?: string
  showDetails?: boolean
}

export interface ValidationErrorNotificationOptions extends Omit<ErrorNotificationOptions, 'title'> {
  title?: string
  field?: string
  validationErrors?: Record<string, string[]>
}

export interface NetworkErrorNotificationOptions extends Omit<ErrorNotificationOptions, 'title'> {
  title?: string
  endpoint?: string
  statusCode?: number
  retryAction?: () => void
}

export interface SystemErrorNotificationOptions extends Omit<ErrorNotificationOptions, 'title'> {
  title?: string
  component?: string
  stack?: string
  reportAction?: () => void
}
