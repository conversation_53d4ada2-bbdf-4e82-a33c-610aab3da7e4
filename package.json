{"name": "engage-planfuly", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "emulator": "firebase emulators:start --import emulatorData --export-on-exit", "deploy": "firebase deploy"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.3.0", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/node": "^24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^5.0.2", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "tw-animate-css": "^1.3.8", "typescript": "~5.8.3", "typescript-eslint": "^8.43.0", "vite": "^7.1.6"}}