import React from 'react'
import { ErrorBoundary } from './ErrorBoundary'
import { DefaultErrorFallback } from './ErrorFallback'
import type { ErrorBoundaryProps, ErrorInfo } from '@/types/error'

interface PageErrorBoundaryProps extends Omit<ErrorBoundaryProps, 'level' | 'fallback'> {
  fallback?: React.ComponentType<any>
}

/**
 * Page-level error boundary that catches errors for entire pages
 * Provides full-page error fallback UI
 */
export function PageErrorBoundary({ 
  children, 
  onError,
  fallback,
  ...props 
}: PageErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // Log page-level errors with more context
    console.error('Page Error Boundary:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    })

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo)
    }

    // Here you could also send to error reporting service
    // reportError({ error, errorInfo, level: 'page' })
  }

  const PageFallback = fallback || DefaultErrorFallback

  return (
    <ErrorBoundary
      level="page"
      fallback={PageFallback}
      onError={handleError}
      {...props}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Section-level error boundary for major page sections
 * Provides section-specific error fallback UI
 */
export function SectionErrorBoundary({ 
  children, 
  onError,
  fallback,
  ...props 
}: PageErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    console.error('Section Error Boundary:', {
      error: error.message,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    })

    if (onError) {
      onError(error, errorInfo)
    }
  }

  const SectionFallback = fallback || DefaultErrorFallback

  return (
    <ErrorBoundary
      level="section"
      fallback={SectionFallback}
      onError={handleError}
      {...props}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Component-level error boundary for individual components
 * Provides minimal error fallback UI
 */
export function ComponentErrorBoundary({ 
  children, 
  onError,
  fallback,
  ...props 
}: PageErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    console.warn('Component Error Boundary:', {
      error: error.message,
      componentStack: errorInfo.componentStack?.split('\n')[0], // Just the first line
      timestamp: new Date().toISOString()
    })

    if (onError) {
      onError(error, errorInfo)
    }
  }

  const ComponentFallback = fallback || DefaultErrorFallback

  return (
    <ErrorBoundary
      level="component"
      fallback={ComponentFallback}
      onError={handleError}
      {...props}
    >
      {children}
    </ErrorBoundary>
  )
}
