import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface Heading3Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading3 = forwardRef<HTMLHeadingElement, Heading3Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h3
        ref={ref}
        className={cn(
          "text-3xl font-bold leading-normal text-foreground",
          className
        )}
        {...props}
      >
        {children}
      </h3>
    );
  }
);

Heading3.displayName = 'Heading3';

export { Heading3 };
