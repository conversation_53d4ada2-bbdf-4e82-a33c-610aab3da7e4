import { clientRepository } from '@/lib/repositories'
import type { 
  Client, 
  CreateClientData, 
  UpdateClientData,
  RepositoryResult,
  SubscriptionCallback,
  SubscriptionErrorCallback
} from '@/types'
import type { Unsubscribe } from 'firebase/firestore'

/**
 * Client service layer that handles business logic and coordinates with the repository
 * This layer can be extended with complex business rules, validation, and data transformation
 */
export class ClientService {
  /**
   * Subscribe to all clients with real-time updates
   */
  subscribeToClients(
    onData: SubscriptionCallback<Client>,
    onError: SubscriptionErrorCallback
  ): Unsubscribe {
    return clientRepository.subscribeToAll(onData, onError)
  }

  /**
   * Create a new client
   */
  async createClient(data: CreateClientData): Promise<RepositoryResult<Client>> {
    // Add any business logic here (e.g., data transformation, additional validation)
    
    // Normalize the data
    const normalizedData: CreateClientData = {
      ...data,
      name: data.name.trim(),
      contactEmail: data.contactEmail?.trim().toLowerCase() || undefined,
      contactPhone: data.contactPhone?.trim() || undefined,
    }

    // Additional business validation
    if (normalizedData.contactEmail && !this.isValidEmail(normalizedData.contactEmail)) {
      return {
        success: false,
        error: 'Please provide a valid email address',
      }
    }

    return clientRepository.createClient(normalizedData)
  }

  /**
   * Update an existing client
   */
  async updateClient(id: string, data: UpdateClientData): Promise<RepositoryResult<Client>> {
    // Normalize the data
    const normalizedData: UpdateClientData = {
      ...data,
    }

    if (data.name !== undefined) {
      normalizedData.name = data.name.trim()
    }

    if (data.contactEmail !== undefined) {
      normalizedData.contactEmail = data.contactEmail?.trim().toLowerCase() || undefined
    }

    if (data.contactPhone !== undefined) {
      normalizedData.contactPhone = data.contactPhone?.trim() || undefined
    }

    // Additional business validation
    if (normalizedData.contactEmail && !this.isValidEmail(normalizedData.contactEmail)) {
      return {
        success: false,
        error: 'Please provide a valid email address',
      }
    }

    return clientRepository.updateClient(id, normalizedData)
  }

  /**
   * Delete a client
   */
  async deleteClient(id: string): Promise<RepositoryResult<void>> {
    // Add any business logic here (e.g., check for dependencies, soft delete, etc.)
    return clientRepository.deleteClient(id)
  }

  /**
   * Get client statistics (example of business logic)
   */
  getClientStats(clients: Client[]) {
    return {
      total: clients.length,
      active: clients.filter(c => c.status === 'Active').length,
      prospects: clients.filter(c => c.status === 'Prospect').length,
      inactive: clients.filter(c => c.status === 'Inactive').length,
    }
  }

  /**
   * Simple email validation
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
}

// Export a singleton instance
export const clientService = new ClientService()
