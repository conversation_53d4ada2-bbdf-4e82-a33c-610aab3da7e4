import type { ReactNode } from "react"
import { Footer } from "@/components/ui/nav/Footer"
import { Header } from "@/components/ui/nav/Header"
import { Heading3 } from "@/components/ui/typography/Heading3"

interface LayoutProps {
  children: ReactNode
  className?: string
  title?: string
}

export function Layout({ children, className, title }: LayoutProps) {
  return (
    <div className={`min-h-svh flex flex-col ${className || ""}`}>
      <Header />

      <Heading3 className="text-center">{title}</Heading3>
      <main className="flex-1 flex flex-col">
        {children}
      </main>
      <Footer />
    </div>
  )
}
