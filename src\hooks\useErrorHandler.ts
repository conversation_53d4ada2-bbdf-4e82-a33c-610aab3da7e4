import { useCallback, useEffect } from 'react'
import { useNotifications } from '@/contexts/NotificationContext'
import { globalErrorHandler } from '@/lib/error/errorHandler'
import type { CategorizedError } from '@/types/error'

interface UseErrorHandlerOptions {
  showNotifications?: boolean
  enableRetry?: boolean
  context?: Record<string, any>
}

interface HandleErrorOptions {
  context?: Record<string, any>
  silent?: boolean
  showNotification?: boolean
  level?: 'page' | 'section' | 'component'
  retryAction?: () => Promise<any>
}

/**
 * Hook that provides error handling with notification integration
 */
export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const notifications = useNotifications()

  // Set up notification handler for the global error handler
  useEffect(() => {
    const notificationHandler = (error: CategorizedError, handlerOptions: HandleErrorOptions) => {
      if (!options.showNotifications && handlerOptions.showNotification !== true) {
        return
      }

      const { category, userMessage, retryable } = error
      const retryAction = handlerOptions.retryAction

      switch (category) {
        case 'validation':
          notifications.showValidationError({
            title: 'Validation Error',
            message: userMessage,
            error,
            duration: 5000
          })
          break

        case 'network':
          notifications.showNetworkError({
            title: 'Network Error',
            message: userMessage,
            error,
            retryAction: retryable && retryAction ? retryAction : undefined,
            duration: 8000
          })
          break

        case 'authentication':
          notifications.showError({
            title: 'Authentication Error',
            message: userMessage,
            error,
            duration: 7000
          })
          break

        case 'authorization':
          notifications.showError({
            title: 'Permission Denied',
            message: userMessage,
            error,
            duration: 7000
          })
          break

        case 'system':
          notifications.showSystemError({
            title: 'System Error',
            message: userMessage,
            error,
            component: handlerOptions.level,
            duration: 0 // Persistent for system errors
          })
          break

        default:
          notifications.showError({
            title: 'Error',
            message: userMessage,
            error,
            duration: 6000
          })
      }
    }

    globalErrorHandler.setNotificationHandler(notificationHandler)
  }, [notifications, options.showNotifications])

  // Handle error function
  const handleError = useCallback(async (
    error: Error | unknown,
    handlerOptions: HandleErrorOptions = {}
  ) => {
    const mergedOptions = {
      context: { ...options.context, ...handlerOptions.context },
      showNotification: options.showNotifications !== false,
      ...handlerOptions
    }

    return globalErrorHandler.handleError(error, mergedOptions)
  }, [options])

  // Handle async operations
  const handleAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    handlerOptions: HandleErrorOptions = {}
  ) => {
    const mergedOptions = {
      context: { ...options.context, ...handlerOptions.context },
      showNotification: options.showNotifications !== false,
      ...handlerOptions
    }

    return globalErrorHandler.handleAsync(operation, mergedOptions)
  }, [options])

  // Handle async operations with retry
  const handleAsyncWithRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    handlerOptions: HandleErrorOptions & { maxRetries?: number; retryDelay?: number } = {}
  ) => {
    const mergedOptions = {
      context: { ...options.context, ...handlerOptions.context },
      showNotification: options.showNotifications !== false,
      ...handlerOptions
    }

    return globalErrorHandler.handleAsyncWithRetry(operation, mergedOptions)
  }, [options])

  // Convenience methods for different error types
  const handleValidationError = useCallback((
    message: string,
    details?: Record<string, string[]>
  ) => {
    notifications.showValidationError({
      title: 'Validation Error',
      message,
      validationErrors: details
    })
  }, [notifications])

  const handleNetworkError = useCallback((
    message: string,
    retryAction?: () => void
  ) => {
    notifications.showNetworkError({
      title: 'Network Error',
      message,
      retryAction
    })
  }, [notifications])

  const handleSystemError = useCallback((
    message: string,
    component?: string
  ) => {
    notifications.showSystemError({
      title: 'System Error',
      message,
      component
    })
  }, [notifications])

  return {
    handleError,
    handleAsync,
    handleAsyncWithRetry,
    handleValidationError,
    handleNetworkError,
    handleSystemError,
    // Direct access to notifications for custom handling
    notifications
  }
}

/**
 * Hook for handling errors in a specific context (e.g., a page or component)
 */
export function useContextualErrorHandler(
  context: Record<string, any>,
  options: Omit<UseErrorHandlerOptions, 'context'> = {}
) {
  return useErrorHandler({ ...options, context })
}

/**
 * Hook for handling errors with automatic retry functionality
 */
export function useRetryableErrorHandler(options: UseErrorHandlerOptions = {}) {
  return useErrorHandler({ ...options, enableRetry: true })
}
