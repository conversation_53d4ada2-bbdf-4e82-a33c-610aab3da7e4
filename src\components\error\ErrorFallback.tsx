import { useState } from 'react'
import { AlertTriangle, RefreshCw, Home, ChevronDown, ChevronUp, Bug } from 'lucide-react'
import { Button } from '@/components/ui/basic/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/container/card'
import { Alert, AlertDescription } from '@/components/ui/feedback/Alert'
import { Heading2, Heading3, BodyText, SmallText } from '@/components/ui/typography'
import type { ErrorFallbackProps } from '@/types/error'

/**
 * Default error fallback component for page-level errors
 */
export function DefaultErrorFallback({ error, errorInfo, resetError, level = 'component' }: ErrorFallbackProps) {
  const [showDetails, setShowDetails] = useState(false)

  const handleGoHome = () => {
    window.location.href = '/'
  }

  const handleReload = () => {
    window.location.reload()
  }

  const handleReportError = () => {
    // This would integrate with your error reporting service
    console.log('Reporting error:', { error, errorInfo })
    // You could open a modal, send to an API, etc.
  }

  if (level === 'page') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="max-w-md w-full space-y-6">
          <div className="text-center">
            <AlertTriangle className="mx-auto h-12 w-12 text-destructive mb-4" />
            <Heading2 className="text-destructive mb-2">Something went wrong</Heading2>
            <BodyText className="text-muted-foreground">
              We're sorry, but something unexpected happened. Please try refreshing the page or go back to the home page.
            </BodyText>
          </div>

          <div className="space-y-3">
            <Button onClick={resetError} className="w-full" variant="default">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button onClick={handleReload} className="w-full" variant="outline">
              Reload Page
            </Button>
            <Button onClick={handleGoHome} className="w-full" variant="outline">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center justify-between">
                  <span>Error Details (Development)</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowDetails(!showDetails)}
                  >
                    {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </CardTitle>
              </CardHeader>
              {showDetails && (
                <CardContent>
                  <div className="space-y-2 text-xs">
                    <div>
                      <strong>Error:</strong>
                      <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-auto">
                        {error?.message || 'Unknown error'}
                      </pre>
                    </div>
                    {error?.stack && (
                      <div>
                        <strong>Stack:</strong>
                        <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-auto max-h-32">
                          {error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          )}

          <div className="text-center">
            <Button variant="ghost" size="sm" onClick={handleReportError}>
              <Bug className="mr-2 h-3 w-3" />
              Report this issue
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (level === 'section') {
    return (
      <Card className="border-destructive/20 bg-destructive/5">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
            <div className="flex-1 space-y-3">
              <div>
                <Heading3 className="text-destructive mb-1">Section Error</Heading3>
                <BodyText className="text-sm text-muted-foreground">
                  This section encountered an error and couldn't load properly.
                </BodyText>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button onClick={resetError} size="sm" variant="outline">
                  <RefreshCw className="mr-2 h-3 w-3" />
                  Retry
                </Button>
                {process.env.NODE_ENV === 'development' && (
                  <Button 
                    onClick={() => setShowDetails(!showDetails)} 
                    size="sm" 
                    variant="ghost"
                  >
                    {showDetails ? 'Hide' : 'Show'} Details
                  </Button>
                )}
              </div>

              {showDetails && process.env.NODE_ENV === 'development' && (
                <Alert variant="error">
                  <AlertDescription>
                    <div className="space-y-2 text-xs">
                      <div><strong>Error:</strong> {error?.message || 'Unknown error'}</div>
                      {error?.stack && (
                        <details>
                          <summary className="cursor-pointer">Stack trace</summary>
                          <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-auto max-h-32">
                            {error.stack}
                          </pre>
                        </details>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Component level (default)
  return (
    <Alert variant="error" className="my-4">
      <AlertTriangle className="h-4 w-4" />
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">Component Error</div>
          <AlertDescription className="mt-1">
            This component encountered an error and couldn't render properly.
          </AlertDescription>
        </div>
        <Button onClick={resetError} size="sm" variant="outline">
          <RefreshCw className="mr-2 h-3 w-3" />
          Retry
        </Button>
      </div>
      
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-3">
          <summary className="cursor-pointer text-sm font-medium">Error Details (Development)</summary>
          <div className="mt-2 space-y-2 text-xs">
            <div><strong>Error:</strong> {error?.message || 'Unknown error'}</div>
            {error?.stack && (
              <div>
                <strong>Stack:</strong>
                <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-auto max-h-24">
                  {error.stack}
                </pre>
              </div>
            )}
          </div>
        </details>
      )}
    </Alert>
  )
}

/**
 * Minimal error fallback for critical components
 */
export function MinimalErrorFallback({ resetError }: ErrorFallbackProps) {
  return (
    <div className="flex items-center justify-center p-4 border border-destructive/20 rounded-lg bg-destructive/5">
      <div className="text-center space-y-2">
        <AlertTriangle className="h-6 w-6 text-destructive mx-auto" />
        <SmallText className="text-destructive">Something went wrong</SmallText>
        <Button onClick={resetError} size="sm" variant="outline">
          Retry
        </Button>
      </div>
    </div>
  )
}

/**
 * Network error specific fallback
 */
export function NetworkErrorFallback({ resetError }: ErrorFallbackProps) {
  return (
    <Alert variant="warning">
      <AlertTriangle className="h-4 w-4" />
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">Connection Error</div>
          <AlertDescription className="mt-1">
            Unable to connect to the server. Please check your internet connection.
          </AlertDescription>
        </div>
        <Button onClick={resetError} size="sm" variant="outline">
          <RefreshCw className="mr-2 h-3 w-3" />
          Retry
        </Button>
      </div>
    </Alert>
  )
}
