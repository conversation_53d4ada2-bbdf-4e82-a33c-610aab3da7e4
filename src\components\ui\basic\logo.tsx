import planfulyLogo from '/logo/Planfuly_Logo.png'
import planfulyLogoMark from '/logo/Planfuly_Logo_Mark.png'
import planfulyWordMark from '/logo/Planfuly_Word_Mark.png'

interface LogoProps {
  className?: string
  height?: number
}

export function Logo({ className, height = 50 }: LogoProps) {
  return (
    <div className={className}>
      <img 
        src={planfulyLogo} 
        className="logo" 
        alt="Planfuly logo" 
        style={{ height: `${height}px` }}
      />
    </div>
  )
}


export function LogoMark({ className, height = 50 }: LogoProps) {
  return (
    <div className={className}>
      <img 
        src={planfulyLogoMark} 
        className="logo" 
        alt="Planfuly logo mark" 
        style={{ height: `${height}px` }}
      />
    </div>
  )
}


export function TextMark({ className, height = 50 }: LogoProps) {
  return (
    <div className={className}>
      <img 
        src={planfulyWordMark} 
        className="logo" 
        alt="Planfuly text mark" 
        style={{ height: `${height}px` }}
      />
    </div>
  )
}
