import { <PERSON><PERSON> } from "@/components/ui/basic/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/container/card"
import planfulyLogoLight from '/logo/Planfuly_Logo_Mark.png'
import planfulyLogoDark from '/logo/Planfuly_Logo_Mark_DarkMode.png'
import { useState } from "react"
import { auth, googleProvider } from "@/lib/firebase"
import { signInWithPopup } from "firebase/auth"
import { FirebaseError } from "firebase/app"

const getFriendlyAuthError = (err: unknown): string | null => {
  const code = (err as FirebaseError | undefined)?.code
  switch (code) {
    case 'auth/popup-closed-by-user':
    case 'auth/cancelled-popup-request':
      return null
    case 'auth/popup-blocked':
      return 'Your browser blocked the sign-in popup. Please allow pop-ups and try again.'
    case 'auth/network-request-failed':
      return 'Network error. Please check your internet connection and try again.'
    case 'auth/unauthorized-domain':
      return 'This app’s domain isn’t authorized for Google sign-in.'
    case 'auth/account-exists-with-different-credential':
      return 'An account already exists with the same email using a different sign-in method. Please use that method.'
    default:
      return 'We couldn’t sign you in with Google. Please try again.'
  }
}

interface LoginProps {
  onLogin: () => void
}

export function Login({ onLogin }: LoginProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleGoogleSignIn = async () => {
    setLoading(true)
    setError(null)
    try {
      await signInWithPopup(auth, googleProvider)
      onLogin()
    } catch (e: unknown) {
      const friendly = getFriendlyAuthError(e)
      if (friendly) {
        setError(friendly)
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-4 relative shadow-lg dark:shadow-[0_10px_15px_-3px_var(--blue-light-gray),_0_4px_6px_-4px_var(--blue-light-gray)]">
      {/* Logo Tab */}
      <div className="absolute -top-28 left-1/2 transform -translate-x-1/2">
        <div className="bg-card rounded-t-lg flex items-center justify-center">
          <img
            src={planfulyLogoLight}
            className="h-30 w-auto m-6 dark:hidden"
            alt="Planfuly logo light"
          />
          <img
            src={planfulyLogoDark}
            className="h-30 w-auto m-6 hidden dark:block"
            alt="Planfuly logo dark"
          />
        </div>
      </div>

      <CardHeader className="pt-12 pb-6">
        {/* <CardTitle className="text-center dark:text-primary"><i>Engage</i> Planfuly</CardTitle> */}
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="flex items-center gap-3">
          
          <Button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="flex-1 h-12 text-base font-medium dark:bg-[var(--blue-medium)] dark:text-white dark:hover:bg-[var(--blue-medium)]/90"
            size="lg"
          >
            {loading ? "Signing in…" : "Continue with Google"}
          </Button>
        </div>
        {error && (
          <p className="text-sm text-red-600 dark:text-red-400 text-center">{error}</p>
        )}
      </CardContent>
    </Card>
  )
}
