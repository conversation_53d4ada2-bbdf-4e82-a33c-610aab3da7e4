import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/container/card"
import { But<PERSON> } from "@/components/ui/basic/button"
import { Mail, Phone, Edit, Trash2 } from "lucide-react"
import { useState } from "react"
import { useClients } from "@/hooks"
import type { Client } from "@/types"

interface ClientCardProps {
  client: Client
}

interface EditData {
  name?: string
  status?: Client['status']
  contactEmail?: string
  contactPhone?: string
}

export function ClientCard({ client }: ClientCardProps) {
  const { updateClient, deleteClient } = useClients()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [editData, setEditData] = useState<EditData>({
    name: client.name,
    status: client.status,
    contactEmail: client.contactEmail,
    contactPhone: client.contactPhone,
  })

  const handleSave = async () => {
    setLoading(true)
    try {
      const result = await updateClient(client.id, editData)
      if (result.success) {
        setIsEditing(false)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete ${client.name}?`)) {
      setLoading(true)
      try {
        await deleteClient(client.id)
      } finally {
        setLoading(false)
      }
    }
  }

  const handleCancel = () => {
    setEditData({
      name: client.name,
      status: client.status,
      contactEmail: client.contactEmail,
      contactPhone: client.contactPhone,
    })
    setIsEditing(false)
  }

  const getStatusColor = (status: Client['status']) => {
    switch (status) {
      case 'Active': return 'text-green-600 bg-green-50 dark:bg-green-950/20'
      case 'Prospect': return 'text-blue-600 bg-blue-50 dark:bg-blue-950/20'
      case 'Inactive': return 'text-gray-600 bg-gray-50 dark:bg-gray-950/20'
      default: return 'text-gray-600 bg-gray-50 dark:bg-gray-950/20'
    }
  }

  return (
    <Card className="relative group">
      {/* Action buttons */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="flex gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
            disabled={loading}
            className="h-8 w-8 p-0"
          >
            <Edit className="size-3" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            disabled={loading}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          >
            <Trash2 className="size-3" />
          </Button>
        </div>
      </div>

      <CardHeader className="pb-2">
        {isEditing ? (
          <div className="space-y-2">
            <input
              type="text"
              value={editData.name || ''}
              onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-2 py-1 text-base font-semibold bg-background border border-input rounded"
              disabled={loading}
            />
            <select
              value={editData.status || client.status}
              onChange={(e) => setEditData(prev => ({ ...prev, status: e.target.value as Client['status'] }))}
              className="w-full px-2 py-1 text-sm bg-background border border-input rounded"
              disabled={loading}
            >
              <option value="Active">Active</option>
              <option value="Prospect">Prospect</option>
              <option value="Inactive">Inactive</option>
            </select>
          </div>
        ) : (
          <>
            <CardTitle className="text-base pr-16">{client.name}</CardTitle>
            <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(client.status)}`}>
              {client.status}
            </div>
          </>
        )}
      </CardHeader>

      <CardContent className="pt-0 text-sm space-y-2">
        {isEditing ? (
          <div className="space-y-2">
            <input
              type="email"
              value={editData.contactEmail || ''}
              onChange={(e) => setEditData(prev => ({ ...prev, contactEmail: e.target.value }))}
              placeholder="Email"
              className="w-full px-2 py-1 text-sm bg-background border border-input rounded"
              disabled={loading}
            />
            <input
              type="tel"
              value={editData.contactPhone || ''}
              onChange={(e) => setEditData(prev => ({ ...prev, contactPhone: e.target.value }))}
              placeholder="Phone"
              className="w-full px-2 py-1 text-sm bg-background border border-input rounded"
              disabled={loading}
            />
            <div className="flex gap-2 pt-2">
              <Button size="sm" onClick={handleSave} disabled={loading}>
                {loading ? 'Saving...' : 'Save'}
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel} disabled={loading}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <>
            {client.contactEmail && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="size-4" />
                <span className="truncate">{client.contactEmail}</span>
              </div>
            )}
            {client.contactPhone && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Phone className="size-4" />
                <span>{client.contactPhone}</span>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
