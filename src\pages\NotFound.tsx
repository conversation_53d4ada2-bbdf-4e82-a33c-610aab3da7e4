import { useNavigate } from "react-router-dom"
import { Home, ArrowLeft, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/basic/button"
import { Card, CardContent } from "@/components/ui/container/card"
import { Heading1, Heading2, BodyText, SmallText } from "@/components/ui/typography"
import { ModeToggle } from "@/components/mode-toggle"

function NotFound() {
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate('/')
  }

  const handleGoBack = () => {
    navigate(-1)
  }


  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="absolute top-4 right-4">
        <ModeToggle />
      </div>

      <div className="max-w-2xl w-full text-center space-y-8">
        {/* Large 404 Display */}
        <div className="space-y-4">
          <Heading1 className="text-8xl md:text-9xl font-black text-muted-foreground/20 select-none">
            404
          </Heading1>
          <div className="space-y-2">
            <Heading2 className="text-foreground">Page Not Found</Heading2>
            <BodyText className="text-muted-foreground max-w-md mx-auto">
              Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
            </BodyText>
          </div>
        </div>

        {/* Action Buttons */}
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 space-y-4">
            <div className="space-y-3">
              <Button onClick={handleGoHome} className="w-full" variant="default">
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
              <Button onClick={handleGoBack} className="w-full" variant="ghost">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
            
            <div className="pt-4 border-t border-border">
              <SmallText className="text-center">
                If you believe this is an error, please contact support or try refreshing the page.
              </SmallText>
            </div>
          </CardContent>
        </Card>

        {/* Additional Help */}
        <div className="space-y-2">
          <SmallText className="text-muted-foreground">
            Error Code: 404 • Page Not Found
          </SmallText>
        </div>
      </div>
    </div>
  )
}

export default NotFound
