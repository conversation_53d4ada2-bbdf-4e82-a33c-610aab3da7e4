import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface SmallTextProps extends HTMLAttributes<HTMLElement> {
  children: ReactNode;
}

const SmallText = forwardRef<HTMLElement, SmallTextProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <small
        ref={ref}
        className={cn(
          "text-xs font-normal leading-snug text-muted-foreground",
          className
        )}
        {...props}
      >
        {children}
      </small>
    );
  }
);

SmallText.displayName = 'SmallText';

export { SmallText };
