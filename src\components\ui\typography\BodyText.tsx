import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface BodyTextProps extends HTMLAttributes<HTMLParagraphElement> {
  children: ReactNode;
}

const BodyText = forwardRef<HTMLParagraphElement, BodyTextProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn(
          "text-base font-normal leading-relaxed text-foreground",
          className
        )}
        {...props}
      >
        {children}
      </p>
    );
  }
);

BodyText.displayName = 'BodyText';

export { BodyText };
