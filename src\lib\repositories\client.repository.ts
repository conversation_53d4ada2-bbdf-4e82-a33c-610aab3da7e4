import { BaseRepository } from './base.repository'
import type { Client, CreateClientData, UpdateClientData } from '@/types/client'
import type { RepositoryResult } from '@/types/common'

/**
 * Client repository for managing client data in Firestore
 * Extends BaseRepository to provide client-specific operations
 */
export class ClientRepository extends BaseRepository<Client> {
  constructor() {
    super('clients')
  }

  /**
   * Create a new client with validation
   */
  async createClient(data: CreateClientData): Promise<RepositoryResult<Client>> {
    // Add any client-specific validation here
    if (!data.name?.trim()) {
      return {
        success: false,
        error: 'Client name is required',
      }
    }

    if (!data.status) {
      // Default to Prospect if no status provided
      data.status = 'Prospect'
    }

    return this.create(data)
  }

  /**
   * Update a client with validation
   */
  async updateClient(id: string, data: UpdateClientData): Promise<RepositoryResult<Client>> {
    // Add any client-specific validation here
    if (data.name !== undefined && !data.name?.trim()) {
      return {
        success: false,
        error: 'Client name cannot be empty',
      }
    }

    return this.update(id, data)
  }

  /**
   * Delete a client
   */
  async deleteClient(id: string): Promise<RepositoryResult<void>> {
    return this.delete(id)
  }
}

// Export a singleton instance
export const clientRepository = new ClientRepository()
