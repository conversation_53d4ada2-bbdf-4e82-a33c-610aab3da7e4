import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface Heading2Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading2 = forwardRef<HTMLHeadingElement, Heading2Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h2
        ref={ref}
        className={cn(
          "text-4xl font-bold leading-tight text-foreground",
          className
        )}
        {...props}
      >
        {children}
      </h2>
    );
  }
);

Heading2.displayName = 'Heading2';

export { Heading2 };
