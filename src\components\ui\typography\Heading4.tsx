import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface Heading4Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading4 = forwardRef<HTMLHeadingElement, Heading4Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h4
        ref={ref}
        className={cn(
          "text-xl font-bold leading-relaxed text-foreground",
          className
        )}
        {...props}
      >
        {children}
      </h4>
    );
  }
);

Heading4.displayName = 'Heading4';

export { Heading4 };
